{"name": "one.whispr-app", "version": "1.0.0", "description": "One Whispr - AI-powered transcription and productivity app", "author": "One Whispr Team", "private": true, "main": ".dist/main/electron/main.js", "scripts": {"dev": "cross-env NODE_ENV=development concurrently --kill-others --names \"VITE,ELECTRON\" --prefix-colors \"blue,green\" \"npm run dev:vite\" \"npm run dev:electron\"", "dev:vite": "vite", "dev:electron": "tsc -p tsconfig.electron.json && electron .", "dev-preview": "cross-env NODE_ENV=development npm run build:vite && npm run build:electron && cross-env LOAD_FROM_VITE=false electron .", "dev-microsoft": "cross-env NODE_ENV=development IS_MICROSOFT=true npm run build:vite && npm run build:electron && cross-env LOAD_FROM_VITE=false electron .", "build": "npm run build:vite && npm run build:electron", "build:vite": "vite build", "build:electron": "tsc -p tsconfig.electron.json", "build:files": "npm run build && electron-builder --config electron-builder-direct.json", "build:files:direct": "npm run build && electron-builder --config electron-builder-direct.json", "build:files:microsoft": "npm run build && electron-builder --config electron-builder-microsoft.json", "backend-setup": "tsx python/utils/python-setup.ts", "backend-compile": "tsx python/utils/python-compile.ts", "backend-compile:quick": "tsx python/utils/python-compile.ts --quick-update", "postinstall": "electron-builder install-app-deps && npx @electron/rebuild"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.5.0", "react-hook-form": "^7.55.0", "clsx": "^2.1.1", "class-variance-authority": "^0.7.1", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "zod": "^3.24.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "motion": "^12.6.3", "electron-store": "^8.2.0", "electron-updater": "^6.3.4", "ws": "^8.18.2", "better-sqlite3": "^11.9.1", "lodash": "^4.17.21", "nanoid": "^3.3.7", "7zip-bin": "^5.2.0", "axios": "^1.7.9"}, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.2", "@types/ws": "^8.5.10", "@types/better-sqlite3": "^7.6.13", "@types/lodash": "^4.17.16", "tsx": "^4.19.3", "cross-env": "^7.0.3", "vite": "^6.2.6", "@vitejs/plugin-react": "^4.3.4", "@tailwindcss/vite": "^4.1.3", "tailwindcss": "^4.1.3", "@electron/rebuild": "^4.0.1", "electron": "^35.0.0", "electron-builder": "^26.0.12", "concurrently": "^8.2.1", "@octokit/rest": "^21.1.1", "@stagewise/toolbar-react": "^0.4.5", "tar": "^7.4.3"}}