import { app, Tray, Menu, nativeImage, MenuItemConstructorOptions, nativeTheme } from 'electron';
import path from 'path';
import { getSettingsWindow, createSettingsWindow } from './windows/settings';
import { disposeFeatures } from './features';

let tray: Tray | null = null;
let mlLibrariesReady: boolean = false;

// Create a function to update the tray context menu
function updateTrayMenu() {
  if (!tray) return;
  
  // Build the menu items including the dynamic modes submenu
  const menuItems: MenuItemConstructorOptions[] = [
    {
      label: 'Start/Stop Recording',
    },
    {
      label: 'Transcribe File'
    },
    { type: 'separator' },
    {
      label: 'Switch Modes'
    },
    {
      label: mlLibrariesReady ? 'Open Settings' : 'Loading AI Models...',
      enabled: mlLibrariesReady,
      click: () => {
        if (!mlLibrariesReady) return;

        let window = getSettingsWindow();
        if (!window) {
          window = createSettingsWindow();
        }
        window.show();
        window.focus();
      }
    },
    { type: 'separator' },
    {
      label: 'Quit',
      click: async () => {
        // Set the quitting flag first
        (app as any).isQuitting = true;
        
        try {
          console.log('Graceful shutdown initiated from tray menu');
          
          // Close all windows first - settings window and any others
          const settingsWindow = getSettingsWindow();
          if (settingsWindow) {
            settingsWindow.close();
          }
          
          // Give a moment for windows to close
          await new Promise(resolve => setTimeout(resolve, 500));
          
          // Dispose features (includes Python backend cleanup)
          disposeFeatures();
          
          // Finally quit the app
          app.quit();
        } catch (error) {
          console.error('Error during tray quit:', error);
          // Force quit if there was an error
          app.exit(0);
        }
      }
    }
  ];

  const contextMenu = Menu.buildFromTemplate(menuItems);
  tray.setContextMenu(contextMenu);
}

// Function to update tray icon based on theme
function updateTrayIcon() {
  if (!tray) return;
  
  const isDarkMode = nativeTheme.shouldUseDarkColors;
  const iconFileName = isDarkMode ? 'one.whispr-white.png' : 'one.whispr-black.png';
  
  const iconPath = app.isPackaged
    ? path.join(process.resourcesPath, iconFileName)
    : path.join(process.cwd(), 'src', 'assets', iconFileName);

  const icon = nativeImage.createFromPath(iconPath);
  tray.setImage(icon);
}

export function setupTray(): void {
  // Use white icon by default (will be updated immediately by updateTrayIcon)
  const iconFileName = 'one.whispr-white.png';
  
  const iconPath = app.isPackaged
    ? path.join(process.resourcesPath, iconFileName)
    : path.join(process.cwd(), 'src', 'assets', iconFileName);

  const icon = nativeImage.createFromPath(iconPath);
  
  tray = new Tray(icon);
  tray.setToolTip('Whispr');
  
  // Create initial menu
  updateTrayMenu();
  
  // Update the icon based on current theme
  updateTrayIcon();
  
  // Listen for theme changes
  nativeTheme.on('updated', updateTrayIcon);

  // Handle double-click on tray icon
  tray.on('double-click', () => {
    // Only allow opening settings if ML libraries are ready
    if (!mlLibrariesReady) {
      console.log('[TRAY] ML libraries not ready yet, ignoring double-click');
      return;
    }

    let window = getSettingsWindow();
    if (!window) {
      window = createSettingsWindow();
    }
    window.show();
    window.focus();
  });
}

export function getTray(): Tray | null {
  return tray;
}

// Add a way to refresh the tray menu when modes change
export function refreshTrayMenu(): void {
  updateTrayMenu();
}

/**
 * Set the ML libraries ready state and update the tray menu
 */
export function setMLLibrariesReady(ready: boolean): void {
  console.log(`[TRAY] ML libraries ready state changed: ${ready}`);
  mlLibrariesReady = ready;
  updateTrayMenu(); // Refresh the menu to show/hide settings option
}

/**
 * Get the current ML libraries ready state
 */
export function isMLLibrariesReady(): boolean {
  return mlLibrariesReady;
}
