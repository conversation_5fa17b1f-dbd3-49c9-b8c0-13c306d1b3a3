
import { app } from 'electron';
import { resetStore, createSettingsWindow, createLauncherWindow, showLauncherWindow, getLauncherWindow } from './windows';
import { resetDatabase } from './database/core/connection';
import { initializeFeatures, setupInitialDatabaseSync, setupBackendEventForwarding } from './features';
import { setupTray } from './tray';
import { IS_MICROSOFT } from './constants';
import { getLauncherManager } from './features/launcher';

const RESET_ON_START = true;

// Request a single instance lock to prevent multiple instances
const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  // Quit if we're the second instance
  app.quit();
} else {
  app.whenReady().then(async () => {
    if (RESET_ON_START) {
      resetStore();
      resetDatabase();
    }

    // setup tray
    setupTray();

    // For Microsoft builds, show launcher first
    if (IS_MICROSOFT) {
      console.log('[MAIN] Microsoft build detected - showing launcher first');

      // Create and show launcher window
      createLauncherWindow();
      showLauncherWindow();
      console.log('[MAIN] Launcher window created and shown');

      // Initialize features in background while launcher is shown
      await initializeFeatures();
      console.log('[MAIN] Features initialized successfully');

      // Connect launcher window to launcher manager for progress updates
      const launcherWindow = getLauncherWindow();
      const launcherManager = getLauncherManager();
      if (launcherWindow && launcherManager) {
        launcherManager.setWindow(launcherWindow);
        console.log('[MAIN] Launcher window connected to launcher manager');
      }

      // Create the settings window but don't show it yet (launcher will show it)
      createSettingsWindow();
      console.log('[MAIN] Settings window created');

      // Set up backend event forwarding
      setupBackendEventForwarding();
      console.log('[MAIN] Backend event forwarding set up');

      // Wait for initial database sync to complete
      await setupInitialDatabaseSync();
      console.log('[MAIN] Initial database sync completed');

      // The launcher will handle showing the settings window after ML libraries load
    } else {
      // For direct builds, use the original flow (handled by one.whispr-setup)
      console.log('[MAIN] Direct build detected - using original flow');

      // 1. Initialize all application features (database, backend, IPC)
      await initializeFeatures();
      console.log('[MAIN] Features initialized successfully');

      // 2. Create the settings window but don't show it yet
      createSettingsWindow();
      console.log('[MAIN] Settings window created');

      // 3. Set up backend event forwarding to the renderer now that window exists
      setupBackendEventForwarding();
      console.log('[MAIN] Backend event forwarding set up');

      // 4. Wait for initial database sync to complete
      await setupInitialDatabaseSync();
      console.log('[MAIN] Initial database sync completed');
    }
  });
}
