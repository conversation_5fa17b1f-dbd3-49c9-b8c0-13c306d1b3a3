
import { app } from 'electron';
import { resetStore, createSettingsWindow, createLauncherWindow, getLauncherWindow, showLauncherWindow } from './windows';
import { resetDatabase } from './database/core/connection';
import { initializeFeatures, setupInitialDatabaseSync, setupBackendEventForwarding } from './features';
import { setupTray } from './tray';
import { IS_MICROSOFT } from './constants';
import { getLauncherManager, initializeLauncher } from './features/launcher';

const RESET_ON_START = true;

// Request a single instance lock to prevent multiple instances
const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  // Quit if we're the second instance
  app.quit();
} else {
  app.whenReady().then(async () => {
    if (RESET_ON_START) {
      resetStore();
      resetDatabase();
    }

    // setup tray
    setupTray();

    // Debug: Check environment variables and build detection
    console.log('[MAIN] Environment variables:');
    console.log('[MAIN] NODE_ENV:', process.env.NODE_ENV);
    console.log('[MAIN] IS_MICROSOFT env var:', process.env.IS_MICROSOFT);
    console.log('[MAIN] IS_MICROSOFT constant:', IS_MICROSOFT);
    console.log('[MAIN] Platform:', process.platform);

    // For Microsoft builds, show launcher first (replicate one.whispr-setup flow)
    if (IS_MICROSOFT) {
      console.log('[MAIN] Microsoft build detected - showing launcher first');

      // Initialize launcher feature first to register IPC handlers
      initializeLauncher();
      console.log('[MAIN] Launcher feature initialized');

      // Create launcher window (it will show itself when ready)
      createLauncherWindow();
      showLauncherWindow();
      console.log('[MAIN] Launcher window created');

      // Connect launcher window to launcher manager for progress updates
      const launcherWindow = getLauncherWindow();
      const launcherManager = getLauncherManager();
      if (launcherWindow && launcherManager) {
        launcherManager.setWindow(launcherWindow);
        console.log('[MAIN] Launcher window connected to launcher manager');
      }

      // Listen for launcher completion to initialize main app features
      if (launcherManager) {
        launcherManager.onComplete(async () => {
          console.log('[MAIN] Launcher completed - initializing main app features...');
          try {
            await initializeFeatures(true);
            console.log('[MAIN] Main app features initialized successfully');

            // Set up backend event forwarding to the renderer now that window exists
            setupBackendEventForwarding();
            console.log('[MAIN] Backend event forwarding set up');

            // Wait for initial database sync to complete
            await setupInitialDatabaseSync();
            console.log('[MAIN] Initial database sync completed');
          } catch (error) {
            console.error('[MAIN] Failed to initialize main app features:', error);
          }
        });
      }

      // DO NOT initialize backend yet - launcher will handle this after updates are complete
      console.log('[MAIN] Waiting for launcher to complete update process...');

      // The launcher will handle:
      // 1. Checking for updates
      // 2. Downloading scripts.7z if needed
      // 3. Initializing backend ONLY after updates complete
      // 4. Creating and showing settings window
      // 5. Setting up backend event forwarding and database sync
    } else {
      // For direct builds, use the original flow (handled by one.whispr-setup)
      console.log('[MAIN] Direct build detected - using original flow');

      // 1. Initialize all application features (database, backend, IPC)
      await initializeFeatures();
      console.log('[MAIN] Features initialized successfully');

      // 2. Create the settings window but don't show it yet
      createSettingsWindow();
      console.log('[MAIN] Settings window created');

      // 3. Set up backend event forwarding to the renderer now that window exists
      setupBackendEventForwarding();
      console.log('[MAIN] Backend event forwarding set up');

      // 4. Wait for initial database sync to complete
      await setupInitialDatabaseSync();
      console.log('[MAIN] Initial database sync completed');
    }
  });
}
