import { initializeDatabaseSystem, shutdownDatabaseSystem, sendInitialSync } from '../database';
import { setupAppIPCHandlers } from './app/ipc';
import { setupVocabularyIPCHandlers } from './vocabulary/ipc';
import { setupTextReplacementIPCHandlers } from './text-replacements/ipc';
import { setupModeIPCHandlers } from './modes/ipc';
import { setupSettingsIPCHandlers } from './settings/ipc';
import { setupAIModelIPCHandlers } from './ai-models/ipc';
import { initializeLauncher, disposeLauncher } from './launcher';
import { initializeBackend, terminateBackend } from '../../python/main';
import { ipcMain } from 'electron';
import { getSettingsWindow, showSettingsWindow } from '../windows';
import { sendRequest, isBackendConnected, isBackendRunning, getBackendPort, backendEvents } from '../../python/main';
import { setMLLibrariesReady } from '../tray';
import * as fs from 'fs';
import * as path from 'path';
import { app } from 'electron';
import { IS_MICROSOFT } from '../constants';

/**
 * Sets up initial database sync (without window dependency)
 */
export function setupInitialDatabaseSync(): Promise<void> {
  return new Promise((resolve) => {
    // Set up one-time listener for backend connection
    const onConnected = () => {
      console.log('[MAIN] Backend connected, starting initial database sync...');
      
      sendInitialSync().then(() => {
        console.log('[MAIN] Initial database sync completed successfully');
        resolve();
      }).catch((error: Error) => {
        console.error('[MAIN] Failed to send initial database sync to backend:', error);
        // Still resolve so the app continues (user can see error in UI)
        resolve();
      });
    };

    // One-time event listener
    backendEvents.once('connected', onConnected);
    
    // If already connected, trigger immediately
    if (isBackendConnected()) {
      onConnected();
    }
  });
}

/**
 * Sets up forwarding of backend events to the renderer process
 * (requires window to exist)
 */
export function setupBackendEventForwarding(): void {
  console.log('[MAIN] setupBackendEventForwarding() called');
  const window = getSettingsWindow();
  if (!window) {
    console.warn('No settings window available for Python event forwarding');
    return;
  }

  // Helper function to safely send messages to renderer
  const safeSend = (channel: string, data?: any) => {
    try {
      const currentWindow = getSettingsWindow();
      if (currentWindow && !currentWindow.isDestroyed() && currentWindow.webContents && !currentWindow.webContents.isDestroyed()) {
        if (data !== undefined) {
          currentWindow.webContents.send(channel, data);
        } else {
          currentWindow.webContents.send(channel);
        }
      }
    } catch (error) {
      // Silently ignore errors during shutdown
    }
  };

  // Core events from backend
  backendEvents.on('connected', () => {
    safeSend('python-ws-connected');
    console.log('[MAIN] Backend WebSocket connected - events will be automatically forwarded');
  });

  backendEvents.on('disconnected', () => {
    safeSend('python-ws-connection-failed');
  });

  backendEvents.on('reconnect_failed', () => {
    safeSend('python-ws-connection-failed');
  });

  // The 'message' event contains all messages from backend
  backendEvents.on('message', (message: any) => {
    // Forward all messages, including our new transcription events
    safeSend('python-ws-message', message);
    console.log('Received WebSocket message:', JSON.stringify(message, null, 2));
  });

  // Handle ML libraries initialization completion - signal launcher
  console.log('[MAIN] Setting up ml_libraries_initialized event listener');
  backendEvents.once('ml_libraries_initialized', (data: any) => {
    console.log('[MAIN] ML libraries initialized, signaling launcher');

    // Update tray menu to enable settings
    setMLLibrariesReady(true);

    // Forward event to renderer
    safeSend('python-ml-libraries-initialized', data);

    // For Microsoft builds, also signal the launcher manager directly
    if (IS_MICROSOFT) {
      import('./launcher').then(({ getLauncherManager }) => {
        const launcherManager = getLauncherManager();
        if (launcherManager) {
          console.log('[MAIN] Signaling launcher manager for Microsoft build');
          launcherManager.onMLLibrariesInitialized();
        }
      }).catch(error => {
        console.error('[MAIN] Error importing launcher manager:', error);
      });
    }

    // Update shared state to signal ML libraries are ready (for one.whispr-setup)
    try {
      if (fs.existsSync(sharedStatePath)) {
        const stateData = fs.readFileSync(sharedStatePath, 'utf8');
        const state = JSON.parse(stateData);

        state.mlLibrariesReady = true;
        state.timestamp = Date.now();

        fs.writeFileSync(sharedStatePath, JSON.stringify(state, null, 2));
        console.log('[MAIN] Updated shared state - ML libraries ready');
      } else {
        console.log('[MAIN] No launcher shared state file found - launcher may have already closed');
        // If no launcher state, show settings immediately
        showSettingsWindow();
      }
    } catch (error) {
      console.error('[MAIN] Failed to update shared state:', error);
      // Fallback: show settings immediately
      showSettingsWindow();
    }
  });

  // Setup event listeners for any other dynamic events
  backendEvents.on('server_welcome', (data: any) => {
    safeSend('python-server-welcome', data);
  });

  // Set up shared memory communication with launcher
  const sharedStatePath = path.join(app.getPath('temp'), 'whispr-launcher-state.json');

  // Check if launcher shared state exists
  let launcherState = null;
  try {
    if (fs.existsSync(sharedStatePath)) {
      const stateData = fs.readFileSync(sharedStatePath, 'utf8');
      launcherState = JSON.parse(stateData);
      console.log('[MAIN] Found launcher shared state:', launcherState);
    }
  } catch (error) {
    console.log('[MAIN] No launcher shared state found or error reading it:', error);
  }

  // Watch for launcher state changes
  if (launcherState) {
    fs.watchFile(sharedStatePath, { interval: 100 }, (curr, prev) => {
      // Check if file was modified
      if (curr.mtime > prev.mtime) {
        try {
          const stateData = fs.readFileSync(sharedStatePath, 'utf8');
          const state = JSON.parse(stateData);

          console.log('[MAIN] Launcher state updated:', state);

          // Check if launcher wants us to show settings
          if (state.showSettings && state.launcherClosing) {
            console.log('[MAIN] Launcher signaled to show settings window');

            // Stop watching the file
            fs.unwatchFile(sharedStatePath);

            // Clean up shared state file
            try {
              fs.unlinkSync(sharedStatePath);
            } catch (error) {
              // Ignore cleanup errors
            }

            console.log('[MAIN] Showing settings window now');
            showSettingsWindow();
          }
        } catch (error) {
          console.error('[MAIN] Error reading launcher state:', error);
        }
      }
    });
  }


}

/**
 * Sets up all backend-related IPC handlers
 * Should be called during app initialization
 */
export function setupBackendIPCHandlers(): void {
  // Main command handler for backend
  ipcMain.handle('send-command', async (_event, type: string, args?: any) => {
    try {
      // Check if backend is connected
      if (!isBackendConnected()) {
        console.log(`Backend not connected, command ${type} will be rejected`);
        // For certain commands, return a specific fallback response
        if (type === 'system.status' || type === 'system.ping') {
          return {
            status: 'error',
            message: 'Backend not connected',
            pythonConnected: false
          };
        }
        
        // For other commands, return an error
        return {
          status: 'error',
          message: 'Backend not connected',
          command: type
        };
      }
      
      // Use longer timeout for model loading operations
      const timeout = type === 'models.load' ? 120000 : 30000; // 2 minutes for model loading, 30s for others
      
      return await sendRequest(type, args || {}, timeout);
    } catch (error) {
      console.error(`Error sending command ${type} to backend:`, error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
        command: type
      };
    }
  });
  
  // Handle getting backend status
  ipcMain.handle('python-get-status', () => {
    return {
      pythonRunning: isBackendRunning(),
      pythonConnected: isBackendConnected(),
      port: getBackendPort()
    };
  });

  // Handle backend reconnection
  ipcMain.handle('python-reconnect', async () => {
    try {
      // For now, we'll just return the current status since backend doesn't 
      // export a reconnect function we can call
      return {
        status: 'success',
        pythonConnected: isBackendConnected()
      };
    } catch (error) {
      console.error('Error reconnecting to backend:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Note: setupBackendEventForwarding() is now called separately from main.ts
  // after the settings window is created to ensure proper initialization order
}

/**
 * Initialize all application features
 * @param fromLauncher - Whether this is being called from the launcher after updates complete
 */
export async function initializeFeatures(fromLauncher: boolean = false): Promise<void> {
  try {
    // For Microsoft builds, skip initialization unless called from launcher
    if (IS_MICROSOFT && !fromLauncher) {
      console.log('[FEATURES] Microsoft build - launcher will handle initialization after updates');
      return;
    }

    // Initialize the database system
    initializeDatabaseSystem();

    // Set up IPC handlers
    setupBackendIPCHandlers();
    setupAppIPCHandlers();
    setupVocabularyIPCHandlers();
    setupTextReplacementIPCHandlers();
    setupModeIPCHandlers();
    setupSettingsIPCHandlers();
    setupAIModelIPCHandlers();

    // Initialize launcher for Microsoft builds (if not already done)
    if (IS_MICROSOFT && !fromLauncher) {
      initializeLauncher();
    }

    // Initialize the backend with enhanced error handling
    try {
      await initializeBackend();
      console.log('Backend initialized successfully');
    } catch (backendError) {
      console.error('Failed to initialize backend:', backendError);
      // Don't throw here - allow the app to continue without backend
      // The UI will show connection status and user can retry
      console.log('Application will continue without backend - user can retry connection');
    }

    console.log('Features initialized successfully');
  } catch (error) {
    console.error('Error initializing features:', error);
    throw error;
  }
}

/**
 * Clean up all application features
 */
export function disposeFeatures(): void {
  try {
    // Clean up launcher for Microsoft builds
    if (IS_MICROSOFT) {
      disposeLauncher();
    }

    // Clean up backend
    terminateBackend();

    // Close database connections
    shutdownDatabaseSystem();

    console.log('Features disposed successfully');
  } catch (error) {
    console.error('Error disposing features:', error);
  }
}