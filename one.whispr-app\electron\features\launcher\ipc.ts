import { ipc<PERSON>ain } from 'electron';
import { LauncherManager } from './launcher-manager';
import { IS_MICROSOFT } from '../../constants';

/**
 * Setup IPC handlers for launcher operations
 */
export function setupLauncherIpc(launcherManager: LauncherManager): void {
  console.log('[LAUNCHER] Setting up IPC handlers...');

  // Remove existing handlers first to prevent duplicates
  ipcMain.removeHandler('launcher:check-ready');
  ipcMain.removeHandler('launcher:download-updates');
  ipcMain.removeHandler('launcher:launch-main-app');
  ipcMain.removeHandler('launcher:exit');

  // Check if everything is ready to launch
  ipcMain.handle('launcher:check-ready', async () => {
    try {
      return await launcherManager.checkLaunchReady();
    } catch (error) {
      console.error('[LAUNCHER] Error checking readiness:', error);
      throw error;
    }
  });

  // Download and extract Scripts.7z (combines download + extraction)
  ipcMain.handle('launcher:download-updates', async () => {
    try {
      // First download if needed, then extract
      const downloadResult = await launcherManager.downloadUpdates();
      if (downloadResult) {
        return await launcherManager.extractUpdates();
      }
      return downloadResult;
    } catch (error) {
      console.error('[LAUNCHER] Error downloading/extracting Scripts.7z:', error);
      throw error;
    }
  });

  // Launch main app (for Microsoft builds, this transitions to main app)
  ipcMain.handle('launcher:launch-main-app', async () => {
    try {
      return await launcherManager.launchMainApp();
    } catch (error) {
      console.error('[LAUNCHER] Error launching main app:', error);
      throw error;
    }
  });

  // Get environment variables
  ipcMain.handle('launcher:get-env', async () => {
    return {
      NODE_ENV: process.env.NODE_ENV,
      IS_MICROSOFT: IS_MICROSOFT.toString()
    };
  });

  // Exit launcher
  ipcMain.handle('launcher:exit', async () => {
    try {
      return await launcherManager.exitLauncher();
    } catch (error) {
      console.error('[LAUNCHER] Error exiting launcher:', error);
      throw error;
    }
  });

  console.log('[LAUNCHER] IPC handlers registered');
}
