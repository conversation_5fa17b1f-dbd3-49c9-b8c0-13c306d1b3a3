import axios from 'axios';
import * as fs from 'fs-extra';
import * as path from 'path';
import { BrowserWindow } from 'electron';

interface DownloadProgress {
  percentage: number;
  downloadedBytes: number;
  totalBytes: number;
  downloadSpeed: number;
  phase: string;
  currentFile?: string;
}

/**
 * Handles file downloads for Microsoft Store builds
 */
export class FileDownloader {
  private backendPath: string;
  private window: BrowserWindow | null = null;
  private abortController: AbortController | null = null;

  constructor(backendPath: string, window?: BrowserWindow) {
    this.backendPath = backendPath;
    this.window = window || null;
  }

  /**
   * Set abort controller for cancelling downloads
   */
  setAbortController(controller: AbortController | null): void {
    this.abortController = controller;
  }

  /**
   * Download scripts update from VPS
   */
  async downloadScriptsUpdate(downloadUrl: string): Promise<string> {
    const fileName = 'OneWhispr-Scripts.7z';
    const filePath = path.join(this.backendPath, fileName);

    console.log(`[DOWNLOADER] Downloading scripts update from ${downloadUrl}`);
    
    this.sendProgress({
      percentage: 0,
      downloadedBytes: 0,
      totalBytes: 0,
      downloadSpeed: 0,
      phase: 'Downloading scripts update...',
      currentFile: fileName
    });

    await this.downloadFile(downloadUrl, filePath);
    
    console.log(`[DOWNLOADER] Scripts update downloaded to ${filePath}`);
    return filePath;
  }

  /**
   * Download a file with progress reporting
   */
  private async downloadFile(url: string, filePath: string): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        console.log(`[DOWNLOADER] Downloading from ${url} to ${filePath}`);

        // Ensure directory exists
        await fs.ensureDir(path.dirname(filePath));

        // Create write stream
        const writer = fs.createWriteStream(filePath);

        // Track progress
        let downloadedBytes = 0;
        const startTime = Date.now();
        let lastUpdateTime = startTime;
        let lastBytes = 0;
        let speed = 0;

        // Calculate timeout based on expected file size (minimum 60s, +30s per 50MB)
        const timeoutMs = 120000; // 2 minutes for scripts updates (usually small)
        console.log(`[DOWNLOADER] Download timeout: ${timeoutMs / 1000}s`);

        // Download the file
        const response = await axios({
          method: 'get',
          url: url,
          responseType: 'stream',
          signal: this.abortController?.signal,
          timeout: timeoutMs,
          headers: {
            'User-Agent': 'OneWhispr-App/1.0.0'
          }
        });

        const totalBytes = parseInt(response.headers['content-length'] || '0', 10);
        console.log(`[DOWNLOADER] File size: ${(totalBytes / 1024 / 1024).toFixed(1)}MB`);

        // Handle download progress
        response.data.on('data', (chunk: Buffer) => {
          downloadedBytes += chunk.length;
          
          // Calculate speed every 500ms
          const now = Date.now();
          if (now - lastUpdateTime >= 500) {
            const timeDiff = (now - lastUpdateTime) / 1000;
            const bytesDiff = downloadedBytes - lastBytes;
            speed = bytesDiff / timeDiff;
            
            const percentage = totalBytes > 0 ? (downloadedBytes / totalBytes) * 100 : 0;
            
            this.sendProgress({
              percentage,
              downloadedBytes,
              totalBytes,
              downloadSpeed: speed,
              phase: 'Downloading scripts update...',
              currentFile: path.basename(filePath)
            });
            
            lastUpdateTime = now;
            lastBytes = downloadedBytes;
          }
        });

        // Handle completion
        response.data.on('end', () => {
          console.log(`[DOWNLOADER] Download completed: ${filePath}`);
          
          this.sendProgress({
            percentage: 100,
            downloadedBytes: totalBytes || downloadedBytes,
            totalBytes: totalBytes || downloadedBytes,
            downloadSpeed: 0,
            phase: 'Download complete',
            currentFile: path.basename(filePath)
          });
          
          resolve();
        });

        // Handle errors
        response.data.on('error', (error: Error) => {
          console.error(`[DOWNLOADER] Download error:`, error);
          writer.destroy();
          fs.remove(filePath).catch(() => {}); // Clean up partial file
          reject(error);
        });

        writer.on('error', (error: Error) => {
          console.error(`[DOWNLOADER] Write error:`, error);
          fs.remove(filePath).catch(() => {}); // Clean up partial file
          reject(error);
        });

        // Pipe the response to file
        response.data.pipe(writer);

      } catch (error) {
        console.error(`[DOWNLOADER] Error downloading file:`, error);
        
        // Clean up partial file
        try {
          await fs.remove(filePath);
        } catch (cleanupError) {
          // Ignore cleanup errors
        }
        
        reject(error);
      }
    });
  }

  /**
   * Send progress update to renderer
   */
  private sendProgress(progress: DownloadProgress): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.webContents.send('launcher:progress', progress);
    }
  }

  /**
   * Verify file checksum
   */
  async verifyChecksum(filePath: string, expectedChecksum: string): Promise<boolean> {
    try {
      const crypto = require('crypto');
      const fileBuffer = await fs.readFile(filePath);
      const hash = crypto.createHash('sha256').update(fileBuffer).digest('hex');
      
      const isValid = hash.toLowerCase() === expectedChecksum.toLowerCase();
      console.log(`[DOWNLOADER] Checksum verification: ${isValid ? 'PASSED' : 'FAILED'}`);
      
      return isValid;
    } catch (error) {
      console.error('[DOWNLOADER] Error verifying checksum:', error);
      return false;
    }
  }
}
