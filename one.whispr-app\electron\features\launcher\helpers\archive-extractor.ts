import * as fs from 'fs-extra';
import * as path from 'path';
import { spawn } from 'child_process';
import { BrowserWindow } from 'electron';
import sevenBin from '7zip-bin';

interface ExtractionProgress {
  percentage: number;
  phase: string;
  currentFile?: string;
}

/**
 * Handles 7zip archive extraction for Microsoft Store builds
 */
export class ArchiveExtractor {
  private window: BrowserWindow | null = null;

  constructor(_backendPath: string, window?: BrowserWindow) {
    this.window = window || null;
  }

  /**
   * Extract 7z archive using 7zip-bin
   */
  async extract7z(archivePath: string, extractPath: string): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        console.log(`[EXTRACTOR] Extracting ${archivePath} to ${extractPath}`);

        // Verify the archive file exists and is readable
        if (!fs.existsSync(archivePath)) {
          throw new Error(`Archive file does not exist: ${archivePath}`);
        }

        const archiveStats = await fs.stat(archivePath);
        if (archiveStats.size === 0) {
          throw new Error(`Archive file is empty: ${archivePath}`);
        }

        console.log(`[EXTRACTOR] Archive size: ${(archiveStats.size / 1024 / 1024).toFixed(1)}MB`);

        // Ensure extraction directory exists
        await fs.ensureDir(extractPath);

        const sevenZipPath = sevenBin.path7za;
        console.log(`[EXTRACTOR] Using 7zip binary: ${sevenZipPath}`);

        const extractType = archivePath.includes('Runtime') ? 'runtime' : 'scripts';

        // Arguments for 7zip extraction
        const args = [
          'x',                    // extract command
          archivePath,           // source file
          `-o${extractPath}`,    // output directory (no space after -o)
          '-y',                  // yes to all prompts
          '-aoa'                 // overwrite all files without prompting
        ];

        console.log(`[EXTRACTOR] Running command: ${sevenZipPath} ${args.join(' ')}`);

        let stdout = '';
        let stderr = '';

        // Simple progress simulation while extraction runs
        let simulatedProgress = 0;
        const progressInterval = setInterval(() => {
          simulatedProgress += 5;
          if (simulatedProgress <= 95) {
            this.sendProgress({
              percentage: simulatedProgress,
              phase: `Extracting ${extractType}...`,
              currentFile: undefined
            });
          }
        }, 2000); // Update every 2 seconds

        const process = spawn(sevenZipPath, args, {
          stdio: ['pipe', 'pipe', 'pipe'],
          windowsHide: true
        });

        process.stdout.on('data', (data) => {
          stdout += data.toString();
        });

        process.stderr.on('data', (data) => {
          stderr += data.toString();
        });

        process.on('close', async (code) => {
          clearInterval(progressInterval);

          if (code === 0) {
            console.log(`[EXTRACTOR] Extraction completed successfully`);
            
            // Send final progress
            this.sendProgress({
              percentage: 100,
              phase: `${extractType} extraction complete`,
              currentFile: undefined
            });

            // Clean up info files if version files exist
            await this.cleanupInfoFiles(extractPath);

            resolve();
          } else {
            console.error(`[EXTRACTOR] Extraction failed with code ${code}`);
            console.error(`[EXTRACTOR] stderr: ${stderr}`);
            console.error(`[EXTRACTOR] stdout: ${stdout}`);
            reject(new Error(`7zip extraction failed with code ${code}: ${stderr}`));
          }
        });

        process.on('error', (error) => {
          clearInterval(progressInterval);
          console.error(`[EXTRACTOR] Process error:`, error);
          reject(error);
        });

      } catch (error) {
        console.error(`[EXTRACTOR] Error during extraction:`, error);
        reject(error);
      }
    });
  }

  /**
   * Clean up info files if version files exist (Microsoft Store compatibility)
   */
  private async cleanupInfoFiles(extractPath: string): Promise<void> {
    try {
      const infoFiles = [
        'runtime-info.json',
        'scripts-info.json'
      ];

      const versionFiles = [
        'runtime-version.json',
        'scripts-version.json'
      ];

      // Check if any version files exist
      const hasVersionFiles = versionFiles.some(file => 
        fs.existsSync(path.join(extractPath, file))
      );

      if (hasVersionFiles) {
        console.log('[EXTRACTOR] Version files detected, cleaning up info files...');
        
        for (const infoFile of infoFiles) {
          const infoPath = path.join(extractPath, infoFile);
          if (fs.existsSync(infoPath)) {
            await fs.remove(infoPath);
            console.log(`[EXTRACTOR] Removed ${infoFile}`);
          }
        }
      }
    } catch (error) {
      console.error('[EXTRACTOR] Error cleaning up info files:', error);
      // Don't throw - this is not critical
    }
  }

  /**
   * Send progress update to renderer
   */
  private sendProgress(progress: ExtractionProgress): void {
    if (this.window && !this.window.isDestroyed()) {
      this.window.webContents.send('launcher:progress', {
        percentage: progress.percentage,
        downloadedBytes: 0,
        totalBytes: 0,
        downloadSpeed: 0,
        phase: progress.phase,
        currentFile: progress.currentFile
      });
    }
  }
}
