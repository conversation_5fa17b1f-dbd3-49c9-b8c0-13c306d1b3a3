import { ipc<PERSON>ain } from 'electron';
import { setupLauncherIpc } from './ipc';
import { LauncherManager } from './launcher-manager';

let launcherManager: LauncherManager | null = null;

/**
 * Initialize the launcher feature
 */
export function initializeLauncher(): void {
  // Prevent double initialization
  if (launcherManager) {
    console.log('[LAUNCHER] Launcher feature already initialized, skipping...');
    return;
  }

  console.log('[LAUNCHER] Initializing launcher feature...');

  // Create launcher manager
  launcherManager = new LauncherManager();

  // Setup IPC handlers
  setupLauncherIpc(launcherManager);

  console.log('[LAUNCHER] Launcher feature initialized');
}

/**
 * Dispose the launcher feature
 */
export function disposeLauncher(): void {
  console.log('[LAUNCHER] Disposing launcher feature...');
  
  if (launcherManager) {
    launcherManager.dispose();
    launcherManager = null;
  }
  
  // Remove IPC handlers
  ipcMain.removeHandler('launcher:check-ready');
  ipcMain.removeHandler('launcher:check-updates');
  ipcMain.removeHandler('launcher:download-updates');
  ipcMain.removeHandler('launcher:extract-updates');
  ipcMain.removeHandler('launcher:launch-main-app');
  ipcMain.removeHandler('launcher:get-env');
  ipcMain.removeHandler('launcher:exit');
  
  console.log('[LAUNCHER] Launcher feature disposed');
}

/**
 * Get the launcher manager instance
 */
export function getLauncherManager(): LauncherManager | null {
  return launcherManager;
}
