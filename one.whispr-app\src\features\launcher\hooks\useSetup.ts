import { useState, useEffect, useCallback } from 'react';
import { SetupState } from '../types';
import { useSetupEvents } from './useSetupEvents';

/**
 * Hook for managing the setup process
 */
export const useSetup = () => {
  // Setup state
  const [state, setState] = useState<SetupState>({
    // Overall readiness
    launchReady: null,

    // Download state
    downloadNeeded: false,
    downloadReason: '',
    isDownloading: false,
    downloadProgress: null,
    downloadError: null,
    downloadComplete: false,

    // Backend download state
    backendDownloading: false,
    backendProgress: null,
    backendError: null,
    backendComplete: false,

    // Main app state
    mainAppError: null,

    // Overall state
    currentPhase: 'checking'
  });
  
  // Check launch readiness (both main app and backend)
  const checkLaunchReady = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }

      setState(prev => ({ ...prev, currentPhase: 'checking' }));

      const result = await window.electron.checkLauncherReady();

      setState(prev => ({
        ...prev,
        launchReady: result,
        downloadNeeded: result.mainAppNeeded || result.backendNeeded,
        downloadReason: result.reason,
        currentPhase: result.allReady ? 'starting' : 'downloading'
      }));

      return !result.allReady;
    } catch (error) {
      console.error('Failed to check launch readiness:', error);

      setState(prev => ({
        ...prev,
        downloadError: error instanceof Error ? error.message : String(error),
        currentPhase: 'error'
      }));

      return false;
    }
  }, []);
  
  // Start main app download
  const startMainAppDownload = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }

      setState(prev => ({
        ...prev,
        isDownloading: true,
        downloadError: null,
        currentPhase: 'downloading'
      }));

      const result = await window.electron.startDownload();

      if (!result) {
        // Don't treat "already in progress" as an error - just log it
        console.log('[SETUP] Main app download already in progress or completed');
        return true; // Return success since download is happening
      }

      return true;
    } catch (error) {
      console.error('Failed to start main app download:', error);

      setState(prev => ({
        ...prev,
        isDownloading: false,
        downloadError: error instanceof Error ? error.message : String(error),
        currentPhase: 'error'
      }));

      return false;
    }
  }, []);

  // Start backend download
  const startBackendDownload = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }

      setState(prev => ({ ...prev, backendError: null }));

      const result = await window.electron.downloadBackend();

      // Don't treat "already in progress" as an error - just log it
      if (!result) {
        console.log('[SETUP] Backend download already in progress or completed');
      }
    } catch (error) {
      console.error('[SETUP] Backend download error:', error);
      setState(prev => ({
        ...prev,
        backendError: error instanceof Error ? error.message : 'Unknown error'
      }));
    }
  }, []);
  
  // Launch main app
  const launchMainApp = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }
      
      setState(prev => ({
        ...prev,
        currentPhase: 'starting',
        mainAppError: null
      }));

      const result = await window.electron.launchMainApp();

      if (!result) {
        throw new Error('Failed to launch main app');
      }

      // After launching, wait for the main app to be ready
      setState(prev => ({
        ...prev,
        currentPhase: 'waiting'
      }));

      return true;
    } catch (error) {
      console.error('Failed to launch main app:', error);
      
      setState(prev => ({
        ...prev,
        mainAppError: error instanceof Error ? error.message : String(error),
        currentPhase: 'error'
      }));
      
      return false;
    }
  }, []);
  
  // Exit launcher
  const exitLauncher = useCallback(async () => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }
      
      await window.electron.exitLauncher();
    } catch (error) {
      console.error('Failed to exit launcher:', error);
    }
  }, []);
  
  // Set up event listeners
  useSetupEvents({ setState, launchMainApp });
  
  // Auto-start the setup process
  useEffect(() => {
    console.log('[SETUP] useEffect triggered - starting setup process');

    const startSetup = async () => {
      try {
        // Get environment variables from main process
        if (!window.electron) {
          console.error('[SETUP] Electron API not available');
          throw new Error('Electron API not available');
        }

        console.log('[SETUP] Getting environment variables...');
        const env = await window.electron.getLauncherEnv();
        const isDev = env.NODE_ENV === 'development';
        const isMicrosoft = env.IS_MICROSOFT === 'true';
        console.log('[SETUP] Environment check - isDev:', isDev, 'isMicrosoft:', isMicrosoft);

        if (isDev && !isMicrosoft) {
          // In dev mode without Microsoft flag, launch directly
          console.log('[SETUP] Development mode - launching directly');
          await launchMainApp();
          return;
        }

        // Microsoft mode or production - check for downloads first
        console.log('[SETUP] Microsoft/production mode - checking launch readiness');

        const readiness = await window.electron.checkLauncherReady();
        console.log('[SETUP] Readiness result:', readiness);

        // Update state with the readiness info
        setState(prev => ({
          ...prev,
          launchReady: readiness,
          downloadNeeded: readiness.mainAppNeeded || readiness.backendNeeded,
          downloadReason: readiness.reason,
          currentPhase: readiness.allReady ? 'starting' : 'downloading'
        }));

        const needsDownload = !readiness.allReady;
        console.log('[SETUP] Needs download:', needsDownload);

        if (needsDownload) {
          // For Microsoft builds, we only need backend (scripts) downloads
          console.log('[SETUP] Starting backend download for Microsoft build');
          setState(prev => ({ ...prev, currentPhase: 'downloading' }));
          await startBackendDownload();
        } else {
          console.log('[SETUP] No downloads needed - launching main app');
          setState(prev => ({ ...prev, currentPhase: 'starting' }));
          await launchMainApp();
        }
      } catch (error) {
        console.error('[SETUP] Error in startSetup:', error);
      }
    };

    startSetup();
  }, []); // Empty dependency array - only run once on mount

  // Auto-progression: backend download → launch
  useEffect(() => {
    const checkProgression = async () => {
      if (!state.launchReady) return;

      const backendNeeded = state.launchReady.backendNeeded;
      const backendDone = !backendNeeded || state.backendComplete;

      // Backend download complete, launch main app
      if (backendDone && state.currentPhase === 'downloading') {
        console.log('[SETUP] Backend complete - launching main app');
        setState(prev => ({ ...prev, currentPhase: 'starting' }));
        await launchMainApp();
      }
    };

    checkProgression();
  }, [state.backendComplete, state.launchReady, state.currentPhase, launchMainApp]);
  
  return {
    state,
    actions: {
      checkLaunchReady,
      startMainAppDownload,
      startBackendDownload,
      launchMainApp,
      exitLauncher
    }
  };
};
