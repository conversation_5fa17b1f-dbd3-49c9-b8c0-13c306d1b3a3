import { BrowserWindow, screen, app, nativeImage } from 'electron';
import path from 'path';
import fs from 'fs';

// Extend the App interface
interface ExtendedApp extends Electron.App {
  isQuitting?: boolean;
}

let launcherWindow: BrowserWindow | null = null;

/**
 * Create the launcher window for Microsoft Store builds
 */
export function createLauncherWindow(): BrowserWindow {
  console.log('[LAUNCHER] Creating launcher window...');

  // Get primary display to center the window
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;

  // Small window size for launcher
  const windowWidth = 300;
  const windowHeight = 300;

  // Calculate center position
  const x = Math.round((screenWidth - windowWidth) / 2);
  const y = Math.round((screenHeight - windowHeight) / 2);

  launcherWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    x: x,
    y: y,
    resizable: false,
    movable: false,
    minimizable: false,
    maximizable: false,
    closable: false,
    skipTaskbar: false,
    icon: nativeImage.createFromPath(
      app.isPackaged
        ? path.join(process.resourcesPath, 'icon.ico')
        : path.join(process.cwd(), 'src', 'assets', 'icon.ico')
    ),
    webPreferences: {
      preload: path.join(__dirname, '../preload.js'),
      contextIsolation: true,
      nodeIntegration: false,
      backgroundThrottling: false
    },
    titleBarStyle: 'hidden',
    titleBarOverlay: {
      color: '#00000000',
      symbolColor: '#00000000',
      height: 0,
    },
    show: false,
    frame: false // Remove window frame for clean launcher look
  });

  launcherWindow.once('ready-to-show', () => {
    console.log('[LAUNCHER] Window ready - showing launcher window');
    launcherWindow?.show();
    launcherWindow?.focus();
  });

  // Prevent closing - launcher should stay open until app launches
  launcherWindow.on('close', (e) => {
    if (!(app as ExtendedApp).isQuitting) {
      e.preventDefault();
    }
  });

  // Always load from built files (no hot reload issues)
  console.log('[LAUNCHER] Loading from built files');
  const htmlPath = path.join(__dirname, '../../../renderer/index.html');

  if (!fs.existsSync(htmlPath)) {
    console.error('[LAUNCHER] Could not find HTML file at:', htmlPath);
    throw new Error(`HTML file not found at ${htmlPath}. Run 'npm run build:vite' first.`);
  }

  // Open dev tools in development for debugging
  if (process.env.NODE_ENV === 'development') {
    launcherWindow.webContents.openDevTools();
  }

  // Load the HTML file and navigate to launcher route
  launcherWindow.loadFile(htmlPath).then(() => {
    // Wait for DOM to be ready, then navigate to launcher route
    launcherWindow?.webContents.executeJavaScript(`
      if (document.readyState === 'complete') {
        window.location.hash = '#/launcher';
      } else {
        window.addEventListener('load', () => {
          window.location.hash = '#/launcher';
        });
      }
    `);
  });

  console.log('[LAUNCHER] Launcher window created');
  return launcherWindow;
}

/**
 * Get the launcher window instance
 */
export function getLauncherWindow(): BrowserWindow | null {
  return launcherWindow;
}

/**
 * Show the launcher window
 */
export function showLauncherWindow(): void {
  if (launcherWindow) {
    console.log('[LAUNCHER] Showing launcher window');
    if (launcherWindow.isVisible()) {
      console.log('[LAUNCHER] Window already visible, just focusing');
      launcherWindow.focus();
    } else {
      launcherWindow.show();
      launcherWindow.focus();
    }
  } else {
    console.warn('[LAUNCHER] Cannot show window - not created yet');
  }
}

/**
 * Hide the launcher window
 */
export function hideLauncherWindow(): void {
  if (launcherWindow) {
    console.log('[LAUNCHER] Hiding launcher window');
    launcherWindow.hide();
  }
}

/**
 * Close the launcher window
 */
export function closeLauncherWindow(): void {
  if (launcherWindow) {
    console.log('[LAUNCHER] Closing launcher window');
    (app as ExtendedApp).isQuitting = true;
    launcherWindow.close();
    launcherWindow = null;
  }
}

/**
 * Check if launcher window exists and is visible
 */
export function isLauncherWindowVisible(): boolean {
  return launcherWindow !== null && launcherWindow.isVisible();
}
