/**
 * Application-wide constants for Whispr
 */

// Environment
export const IS_DEV = true; // Set to false for production builds

// Build type detection
export const IS_MICROSOFT = process.env.IS_MICROSOFT === 'true' ||
  (process.platform === 'win32' && process.windowsStore === true);

// URLs
export const SITE_URL = IS_DEV 
  ? 'http://localhost:3000'  // Development
  : 'https://www.whispr.one'; // Production

// Auth URLs
export const AUTH_URLS = {
  login: `${SITE_URL}/login?callbackUrl=${encodeURIComponent('whispr://auth')}`,
  register: `${SITE_URL}/register?callbackUrl=${encodeURIComponent('whispr://auth')}`,
};

// Export all constants
export default {
  IS_DEV,
  IS_MICROSOFT,
  SITE_URL,
  AUTH_URLS
};