import { useEffect } from 'react';
import { SetupState } from '../types';

interface UseSetupEventsProps {
  setState: React.Dispatch<React.SetStateAction<SetupState>>;
  launchMainApp: () => Promise<boolean>;
}

/**
 * Hook for handling setup-related events from the main process
 */
export const useSetupEvents = ({ setState, launchMainApp }: UseSetupEventsProps) => {
  useEffect(() => {
    if (!window.electron) return;

    // Handle main app download progress
    const handleDownloadProgress = (progress: any) => {
      setState(prev => ({
        ...prev,
        downloadProgress: progress,
        isDownloading: true
      }));
    };

    // Handle main app download completion
    const handleDownloadComplete = () => {
      console.log('[SETUP] Main app download completed');
      setState(prev => ({
        ...prev,
        downloadComplete: true,
        isDownloading: false
      }));
    };

    // Handle main app download error
    const handleDownloadError = (error: { error: string; details?: string }) => {
      console.error('[SETUP] Main app download error:', error);
      setState(prev => ({
        ...prev,
        downloadError: error.details || error.error,
        isDownloading: false,
        currentPhase: 'error'
      }));
    };

    // Handle backend download progress
    const handleBackendProgress = (progress: any) => {
      setState(prev => ({
        ...prev,
        backendProgress: progress,
        backendDownloading: true
      }));
    };

    // Handle backend download completion
    const handleBackendComplete = () => {
      console.log('[SETUP] Backend download completed');
      setState(prev => ({
        ...prev,
        backendComplete: true,
        backendDownloading: false
      }));
    };

    // Handle backend download error
    const handleBackendError = (error: { error: string; details?: string }) => {
      console.error('[SETUP] Backend download error:', error);
      setState(prev => ({
        ...prev,
        backendError: error.details || error.error,
        backendDownloading: false
      }));
    };

    // Handle main app error
    const handleMainAppError = (error: { error: string; details?: string }) => {
      console.error('[SETUP] Main app error:', error);
      setState(prev => ({
        ...prev,
        mainAppError: error.details || error.error,
        currentPhase: 'error'
      }));
    };

    // Handle main app ready signal
    const handleMainAppReady = () => {
      console.log('[SETUP] Main app is ready - closing launcher');
      // The launcher should close itself when the main app is ready
      if (window.electron) {
        window.electron.ipcRenderer.invoke('launcher:exit').catch(console.error);
      }
    };

    // Set up event listeners
    const cleanupDownloadProgress = window.electron.onDownloadProgress(handleDownloadProgress);
    const cleanupDownloadComplete = window.electron.onDownloadComplete(handleDownloadComplete);
    const cleanupDownloadError = window.electron.onDownloadError(handleDownloadError);
    const cleanupBackendProgress = window.electron.onBackendProgress(handleBackendProgress);
    const cleanupBackendComplete = window.electron.onBackendComplete(handleBackendComplete);
    const cleanupBackendError = window.electron.onBackendError(handleBackendError);
    const cleanupMainAppError = window.electron.onMainAppError(handleMainAppError);
    const cleanupMainAppReady = window.electron.onMainAppReady(handleMainAppReady);

    // Cleanup function
    return () => {
      cleanupDownloadProgress();
      cleanupDownloadComplete();
      cleanupDownloadError();
      cleanupBackendProgress();
      cleanupBackendComplete();
      cleanupBackendError();
      cleanupMainAppError();
      cleanupMainAppReady();
    };
  }, [setState, launchMainApp]);
};
