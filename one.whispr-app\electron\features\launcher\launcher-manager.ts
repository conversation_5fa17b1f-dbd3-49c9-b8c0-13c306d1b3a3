import { app, BrowserWindow } from 'electron';
import * as fs from 'fs-extra';
import * as path from 'path';
import { IS_MICROSOFT } from '../../constants';
import { closeLauncherWindow } from '../../windows/launcher';
import { showSettingsWindow, createSettingsWindow } from '../../windows/settings';
import { VersionManager } from './helpers/version-manager';
import { FileDownloader } from './helpers/file-downloader';
import { ArchiveExtractor } from './helpers/archive-extractor';

// Note: Backend initialization now happens in the settings window, not here

interface UpdateInfo {
  hasUpdate: boolean;
  updateType: 'scripts' | 'runtime' | 'both';
  scriptsVersion?: string;
  runtimeVersion?: string;
  scriptsSize?: number;
  runtimeSize?: number;
}

/**
 * Launcher for Microsoft Store builds - handles first-time extraction and updates
 */
export class LauncherManager {
  private launcherWindow: BrowserWindow | null = null;
  private backendPath: string;
  private versionManager: VersionManager;
  private fileDownloader: FileDownloader;
  private archiveExtractor: ArchiveExtractor;
  private abortController: AbortController | null = null;
  private isLaunching: boolean = false;
  private isExtracting: boolean = false;
  private completionCallback: (() => Promise<void>) | null = null;

  constructor() {
    // Backend path for Microsoft builds
    this.backendPath = app.isPackaged
      ? path.join(process.resourcesPath, 'backend')
      : path.join(process.cwd(), '.dist', 'One Whispr Backend');

    console.log(`[LAUNCHER] Backend path: ${this.backendPath}`);

    // Ensure backend directory exists
    fs.ensureDirSync(this.backendPath);

    // Initialize components
    this.versionManager = new VersionManager(this.backendPath);
    this.fileDownloader = new FileDownloader(this.backendPath);
    this.archiveExtractor = new ArchiveExtractor(this.backendPath);
  }

  /**
   * Set the launcher window for progress updates
   */
  setWindow(window: BrowserWindow): void {
    this.launcherWindow = window;
    // Recreate the downloader and extractor with the new window for progress updates
    this.fileDownloader = new FileDownloader(this.backendPath, window);
    this.archiveExtractor = new ArchiveExtractor(this.backendPath, window);
  }

  /**
   * Set callback to be called when launcher completes
   */
  onComplete(callback: () => Promise<void>): void {
    this.completionCallback = callback;
  }

  /**
   * Check if everything is ready to launch (backend extraction/updates)
   */
  public async checkLaunchReady(): Promise<{
    backendReady: boolean,
    allReady: boolean,
    backendNeeded: boolean,
    reason: string
  }> {
    try {
      console.log('[LAUNCHER] Checking launch readiness...');

      // First check if we need to extract local files (first launch)
      const localFiles = this.versionManager.checkForLocal7zFiles();
      if (localFiles.hasRuntimeZip || localFiles.hasScriptsZip) {
        console.log('[LAUNCHER] Local 7z files found - first launch extraction needed');
        return {
          backendReady: false,
          allReady: false,
          backendNeeded: true,
          reason: 'First time installation - extracting base runtime and scripts'
        };
      }

      // Check if backend is ready
      const backendReady = this.versionManager.isBackendReady();
      console.log('[LAUNCHER] Backend ready:', backendReady);

      if (!backendReady) {
        return {
          backendReady: false,
          allReady: false,
          backendNeeded: true,
          reason: 'Backend not extracted - extraction needed'
        };
      }

      // For Microsoft builds, check for scripts updates from VPS
      if (IS_MICROSOFT) {
        const updateInfo = await this.versionManager.checkForUpdates();
        if (updateInfo.hasUpdate) {
          return {
            backendReady: true,
            allReady: false,
            backendNeeded: true,
            reason: 'Scripts update available'
          };
        }
      }

      return {
        backendReady: true,
        allReady: true,
        backendNeeded: false,
        reason: 'Ready to launch'
      };
    } catch (error) {
      console.error('[LAUNCHER] Error checking launch readiness:', error);
      return {
        backendReady: false,
        allReady: false,
        backendNeeded: true,
        reason: 'Error checking readiness'
      };
    }
  }

  /**
   * Check for updates (Microsoft builds only check scripts)
   */
  async checkForUpdates(): Promise<UpdateInfo> {
    return await this.versionManager.checkForUpdates();
  }

  /**
   * Download updates
   */
  async downloadUpdates(): Promise<boolean> {
    try {
      console.log('[LAUNCHER] Starting download...');

      // Create abort controller
      this.abortController = new AbortController();
      this.fileDownloader.setAbortController(this.abortController);

      // Check what needs to be downloaded
      const localFiles = this.versionManager.checkForLocal7zFiles();
      
      if (localFiles.hasRuntimeZip || localFiles.hasScriptsZip) {
        // First launch - files are already packaged, no download needed
        console.log('[LAUNCHER] First launch - using packaged files');
        return true;
      }

      // Download scripts update from VPS
      const scriptsInfo = await this.versionManager.fetchVersionInfo('scripts');
      if (scriptsInfo) {
        await this.fileDownloader.downloadScriptsUpdate(scriptsInfo.downloadUrl);
      }

      return true;
    } catch (error) {
      console.error('[LAUNCHER] Error downloading updates:', error);
      return false;
    } finally {
      this.abortController = null;
    }
  }

  /**
   * Extract updates
   */
  async extractUpdates(): Promise<boolean> {
    try {
      // Prevent multiple simultaneous extractions
      if (this.isExtracting) {
        console.log('[LAUNCHER] Extraction already in progress, skipping...');
        return false;
      }

      this.isExtracting = true;
      console.log('[LAUNCHER] Starting extraction...');

      const localFiles = this.versionManager.checkForLocal7zFiles();
      const runtimeZipPath = path.join(this.backendPath, 'OneWhispr-Runtime-Base.7z');
      const scriptsZipPath = path.join(this.backendPath, 'OneWhispr-Scripts.7z');

      const hasRuntimeZip = localFiles.hasRuntimeZip;
      const hasScriptsZip = fs.existsSync(scriptsZipPath);

      // Extract runtime base if available (first launch)
      if (hasRuntimeZip) {
        console.log('[LAUNCHER] Extracting runtime base...');
        await this.archiveExtractor.extract7z(runtimeZipPath, this.backendPath);
        console.log('[LAUNCHER] Runtime base extracted successfully');
      }

      // Extract scripts (first launch or update)
      if (hasScriptsZip) {
        console.log('[LAUNCHER] Extracting scripts...');
        const scriptsPath = path.join(this.backendPath, 'scripts');

        // Clean scripts directory first to avoid file conflicts
        if (await fs.pathExists(scriptsPath)) {
          console.log('[LAUNCHER] Cleaning existing scripts directory...');
          await fs.remove(scriptsPath);
        }
        await fs.ensureDir(scriptsPath);

        await this.archiveExtractor.extract7z(scriptsZipPath, scriptsPath);
        console.log('[LAUNCHER] Scripts extracted successfully');

        // Save version info
        const scriptsInfo = await this.versionManager.fetchVersionInfo('scripts');
        if (scriptsInfo) {
          await fs.writeJson(path.join(scriptsPath, 'scripts-version.json'), {
            version: scriptsInfo.version,
            releaseDate: scriptsInfo.releaseDate,
            installedAt: new Date().toISOString()
          });
        }
      }

      // Clean up info files if version files exist
      console.log('[LAUNCHER] Cleaning up info files...');
      await this.archiveExtractor.cleanupInfoFiles(this.backendPath);

      // ONLY AFTER both extractions are complete, delete the 7z files
      console.log('[LAUNCHER] All extractions complete, cleaning up 7z files...');

      // Small delay to ensure 7zip processes have fully released file handles
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (hasRuntimeZip) {
        console.log('[LAUNCHER] Deleting Runtime Base.7z...');
        try {
          await fs.remove(runtimeZipPath);
        } catch (error) {
          console.warn('[LAUNCHER] Warning: Could not delete Runtime Base.7z:', error);
        }
      }

      if (hasScriptsZip) {
        console.log('[LAUNCHER] Deleting Scripts.7z...');
        try {
          await fs.remove(scriptsZipPath);
        } catch (error) {
          console.warn('[LAUNCHER] Warning: Could not delete Scripts.7z:', error);
        }
      }

      console.log('[LAUNCHER] Extraction completed');

      // Send completion event to frontend (matches one.whispr-setup)
      if (this.launcherWindow && !this.launcherWindow.isDestroyed()) {
        this.launcherWindow.webContents.send('backend:complete');
      }

      // Call completion callback for main process
      if (this.completionCallback) {
        try {
          await this.completionCallback();
        } catch (error) {
          console.error('[LAUNCHER] Error in completion callback:', error);
        }
      }

      return true;
    } catch (error) {
      console.error('[LAUNCHER] Error extracting updates:', error);
      return false;
    } finally {
      // Always reset the extraction flag
      this.isExtracting = false;
    }
  }

  /**
   * Launch main app (for Microsoft builds, this transitions to main app)
   */
  async launchMainApp(): Promise<boolean> {
    // Prevent double launches
    if (this.isLaunching) {
      console.log('[LAUNCHER] Launch already in progress, ignoring duplicate request');
      return true;
    }

    try {
      this.isLaunching = true;
      console.log('[LAUNCHER] Updates complete - creating settings window...');

      // For Microsoft builds, now that updates are complete, initialize main features first
      // This replicates what one.whispr-setup does: updates first, then launch main app

      // For dev-microsoft, use the same shared memory approach as one.whispr-setup
      // since there's no conflict when running standalone

      // Call completion callback for main process (starts backend)
      console.log('[LAUNCHER] Calling completion callback (no updates needed)...');
      if (this.completionCallback) {
        try {
          await this.completionCallback();
          console.log('[LAUNCHER] Completion callback finished successfully');
        } catch (error) {
          console.error('[LAUNCHER] Error in completion callback:', error);
        }
      } else {
        console.warn('[LAUNCHER] No completion callback registered!');
      }

      // NOW create the settings window after backend is ready
      createSettingsWindow();
      console.log('[LAUNCHER] Settings window created');

      // For dev-microsoft, wait for ML libraries using shared memory like one.whispr-setup
      this.waitForMLLibrariesViaSharedMemory();

      return true;
    } catch (error) {
      console.error('[LAUNCHER] Error launching main app:', error);
      this.isLaunching = false;
      return false;
    }
  }

  /**
   * Wait for ML libraries using shared memory (for dev-microsoft builds)
   */
  private waitForMLLibrariesViaSharedMemory(): void {
    console.log('[LAUNCHER] Waiting for ML libraries initialization via shared memory...');

    // Create shared state file for communication
    const sharedStatePath = path.join(app.getPath('temp'), 'whispr-launcher-state.json');
    const initialState = {
      launcherPid: process.pid,
      launcherReady: true,
      mlLibrariesReady: false,
      showSettings: false,
      timestamp: Date.now()
    };

    try {
      fs.writeFileSync(sharedStatePath, JSON.stringify(initialState, null, 2));
      console.log('[LAUNCHER] Shared state file created:', sharedStatePath);
    } catch (error) {
      console.error('[LAUNCHER] Failed to create shared state file:', error);
      // Continue anyway - fallback to timeout
    }

    // Watch for changes to the shared state file
    fs.watchFile(sharedStatePath, { interval: 200 }, (curr, prev) => {
      // Check if file was modified
      if (curr.mtime > prev.mtime) {
        try {
          const stateData = fs.readFileSync(sharedStatePath, 'utf8');
          const state = JSON.parse(stateData);

          console.log('[LAUNCHER] Shared state updated:', state);

          // Check if ML libraries are ready
          if (state.mlLibrariesReady) {
            console.log('[LAUNCHER] Received ML libraries ready signal');

            // Stop watching the file
            fs.unwatchFile(sharedStatePath);

            // Close launcher and show settings
            this.closeLauncher();
          }
        } catch (error) {
          console.error('[LAUNCHER] Error reading shared state:', error);
        }
      }
    });

    // Set a timeout in case the main app doesn't respond
    setTimeout(() => {
      console.log('[LAUNCHER] Timeout waiting for ML libraries, showing settings anyway...');

      // Stop watching
      fs.unwatchFile(sharedStatePath);

      // Close launcher anyway
      this.closeLauncher();
    }, 30000); // 30 second timeout
  }

  /**
   * Handle ML libraries initialization completion (called by features/index.ts)
   */
  onMLLibrariesInitialized(): void {
    console.log('[LAUNCHER] ML libraries initialized - closing launcher and showing settings window');
    this.closeLauncher();
  }



  /**
   * Close the launcher properly and show settings window
   */
  private closeLauncher(): void {
    console.log('[LAUNCHER] Closing launcher and showing settings window');

    // Close the launcher window
    closeLauncherWindow();

    // Show the settings window
    setTimeout(() => {
      showSettingsWindow();
    }, 100);
  }

  /**
   * Exit launcher
   */
  async exitLauncher(): Promise<boolean> {
    try {
      console.log('[LAUNCHER] Exiting launcher...');
      
      // Cancel any ongoing operations
      if (this.abortController) {
        this.abortController.abort();
        this.abortController = null;
      }

      return true;
    } catch (error) {
      console.error('[LAUNCHER] Error exiting launcher:', error);
      return false;
    }
  }

  /**
   * Clean up resources
   */
  public cleanup(): void {
    // Cancel any ongoing operations
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }

    // Reset launching flag
    this.isLaunching = false;
  }

  /**
   * Dispose resources
   */
  dispose(): void {
    this.cleanup();
  }
}
