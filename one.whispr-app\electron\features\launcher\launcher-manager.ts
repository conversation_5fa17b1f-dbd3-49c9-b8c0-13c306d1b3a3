import { app, BrowserWindow } from 'electron';
import * as fs from 'fs-extra';
import * as path from 'path';
import { IS_MICROSOFT } from '../../constants';
import { closeLauncherWindow } from '../../windows/launcher';
import { showSettingsWindow, createSettingsWindow } from '../../windows/settings';
import { VersionManager } from './helpers/version-manager';
import { FileDownloader } from './helpers/file-downloader';
import { ArchiveExtractor } from './helpers/archive-extractor';

// Note: Backend initialization now happens in the settings window, not here

interface UpdateInfo {
  hasUpdate: boolean;
  updateType: 'scripts' | 'runtime' | 'both';
  scriptsVersion?: string;
  runtimeVersion?: string;
  scriptsSize?: number;
  runtimeSize?: number;
}

/**
 * Launcher for Microsoft Store builds - handles first-time extraction and updates
 */
export class LauncherManager {
  private backendPath: string;
  private versionManager: VersionManager;
  private fileDownloader: FileDownloader;
  private archiveExtractor: ArchiveExtractor;
  private abortController: AbortController | null = null;
  private launchTimeout: NodeJS.Timeout | null = null;
  private isLaunching: boolean = false;

  constructor() {
    // Backend path for Microsoft builds
    this.backendPath = app.isPackaged
      ? path.join(process.resourcesPath, 'backend')
      : path.join(process.cwd(), '.dist', 'One Whispr Backend');

    console.log(`[LAUNCHER] Backend path: ${this.backendPath}`);

    // Ensure backend directory exists
    fs.ensureDirSync(this.backendPath);

    // Initialize components
    this.versionManager = new VersionManager(this.backendPath);
    this.fileDownloader = new FileDownloader(this.backendPath);
    this.archiveExtractor = new ArchiveExtractor(this.backendPath);
  }

  /**
   * Set the launcher window for progress updates
   */
  setWindow(window: BrowserWindow): void {
    // Recreate the downloader and extractor with the new window for progress updates
    this.fileDownloader = new FileDownloader(this.backendPath, window);
    this.archiveExtractor = new ArchiveExtractor(this.backendPath, window);
  }

  /**
   * Check if everything is ready to launch (backend extraction/updates)
   */
  public async checkLaunchReady(): Promise<{
    backendReady: boolean,
    allReady: boolean,
    backendNeeded: boolean,
    reason: string
  }> {
    try {
      console.log('[LAUNCHER] Checking launch readiness...');

      // First check if we need to extract local files (first launch)
      const localFiles = this.versionManager.checkForLocal7zFiles();
      if (localFiles.hasRuntimeZip || localFiles.hasScriptsZip) {
        console.log('[LAUNCHER] Local 7z files found - first launch extraction needed');
        return {
          backendReady: false,
          allReady: false,
          backendNeeded: true,
          reason: 'First time installation - extracting base runtime and scripts'
        };
      }

      // Check if backend is ready
      const backendReady = this.versionManager.isBackendReady();
      console.log('[LAUNCHER] Backend ready:', backendReady);

      if (!backendReady) {
        return {
          backendReady: false,
          allReady: false,
          backendNeeded: true,
          reason: 'Backend not extracted - extraction needed'
        };
      }

      // For Microsoft builds, check for scripts updates from VPS
      if (IS_MICROSOFT) {
        const updateInfo = await this.versionManager.checkForUpdates();
        if (updateInfo.hasUpdate) {
          return {
            backendReady: true,
            allReady: false,
            backendNeeded: true,
            reason: 'Scripts update available'
          };
        }
      }

      return {
        backendReady: true,
        allReady: true,
        backendNeeded: false,
        reason: 'Ready to launch'
      };
    } catch (error) {
      console.error('[LAUNCHER] Error checking launch readiness:', error);
      return {
        backendReady: false,
        allReady: false,
        backendNeeded: true,
        reason: 'Error checking readiness'
      };
    }
  }

  /**
   * Check for updates (Microsoft builds only check scripts)
   */
  async checkForUpdates(): Promise<UpdateInfo> {
    return await this.versionManager.checkForUpdates();
  }

  /**
   * Download updates
   */
  async downloadUpdates(): Promise<boolean> {
    try {
      console.log('[LAUNCHER] Starting download...');

      // Create abort controller
      this.abortController = new AbortController();
      this.fileDownloader.setAbortController(this.abortController);

      // Check what needs to be downloaded
      const localFiles = this.versionManager.checkForLocal7zFiles();
      
      if (localFiles.hasRuntimeZip || localFiles.hasScriptsZip) {
        // First launch - files are already packaged, no download needed
        console.log('[LAUNCHER] First launch - using packaged files');
        return true;
      }

      // Download scripts update from VPS
      const scriptsInfo = await this.versionManager.fetchVersionInfo('scripts');
      if (scriptsInfo) {
        await this.fileDownloader.downloadScriptsUpdate(scriptsInfo.downloadUrl);
      }

      return true;
    } catch (error) {
      console.error('[LAUNCHER] Error downloading updates:', error);
      return false;
    } finally {
      this.abortController = null;
    }
  }

  /**
   * Extract updates
   */
  async extractUpdates(): Promise<boolean> {
    try {
      console.log('[LAUNCHER] Starting extraction...');

      const localFiles = this.versionManager.checkForLocal7zFiles();

      // Extract runtime base if available (first launch)
      if (localFiles.hasRuntimeZip) {
        const runtimeZipPath = path.join(this.backendPath, 'OneWhispr-Runtime-Base.7z');
        console.log('[LAUNCHER] Extracting runtime base...');
        await this.archiveExtractor.extract7z(runtimeZipPath, this.backendPath);
        
        // Clean up the archive
        await fs.remove(runtimeZipPath);
      }

      // Extract scripts (first launch or update)
      const scriptsZipPath = path.join(this.backendPath, 'OneWhispr-Scripts.7z');
      if (fs.existsSync(scriptsZipPath)) {
        console.log('[LAUNCHER] Extracting scripts...');
        const scriptsPath = path.join(this.backendPath, 'scripts');
        
        // Clean scripts directory first
        if (await fs.pathExists(scriptsPath)) {
          await fs.remove(scriptsPath);
        }
        await fs.ensureDir(scriptsPath);

        await this.archiveExtractor.extract7z(scriptsZipPath, scriptsPath);
        
        // Clean up the archive
        await fs.remove(scriptsZipPath);

        // Save version info
        const scriptsInfo = await this.versionManager.fetchVersionInfo('scripts');
        if (scriptsInfo) {
          await fs.writeJson(path.join(scriptsPath, 'scripts-version.json'), {
            version: scriptsInfo.version,
            releaseDate: scriptsInfo.releaseDate,
            installedAt: new Date().toISOString()
          });
        }
      }

      console.log('[LAUNCHER] Extraction completed');

      // Send completion event to frontend (matches one.whispr-setup)
      if (this.launcherWindow && !this.launcherWindow.isDestroyed()) {
        this.launcherWindow.webContents.send('backend:complete');
      }

      return true;
    } catch (error) {
      console.error('[LAUNCHER] Error extracting updates:', error);
      return false;
    }
  }

  /**
   * Launch main app (for Microsoft builds, this transitions to main app)
   */
  async launchMainApp(): Promise<boolean> {
    // Prevent double launches
    if (this.isLaunching) {
      console.log('[LAUNCHER] Launch already in progress, ignoring duplicate request');
      return true;
    }

    try {
      this.isLaunching = true;
      console.log('[LAUNCHER] Updates complete - creating settings window...');

      // For Microsoft builds, now that updates are complete, create the settings window
      // The settings window will initialize the backend when it's ready
      // This replicates what one.whispr-setup does: updates first, then launch main app

      // Create the settings window (this will trigger backend initialization)
      createSettingsWindow();
      console.log('[LAUNCHER] Settings window created');

      // Wait for main app to be ready (ML libraries initialized) via shared memory
      // The settings window will initialize features and signal when ready
      this.waitForMainAppReadyViaSharedMemory();

      return true;
    } catch (error) {
      console.error('[LAUNCHER] Error launching main app:', error);
      this.isLaunching = false;
      return false;
    }
  }

  /**
   * Wait for main app to be ready (ML libraries initialized) via shared memory
   */
  private waitForMainAppReadyViaSharedMemory(): void {
    console.log('[LAUNCHER] Waiting for ML libraries initialization...');

    // Create shared state file for communication
    const sharedStatePath = path.join(app.getPath('temp'), 'whispr-launcher-state.json');
    const initialState = {
      launcherPid: process.pid,
      launcherReady: true,
      mlLibrariesReady: false,
      showSettings: false,
      timestamp: Date.now()
    };

    try {
      fs.writeFileSync(sharedStatePath, JSON.stringify(initialState, null, 2));
      console.log('[LAUNCHER] Shared state file created:', sharedStatePath);
    } catch (error) {
      console.error('[LAUNCHER] Failed to create shared state file:', error);
      // Continue anyway - fallback to timeout
    }

    // Watch for changes to the shared state file
    fs.watchFile(sharedStatePath, { interval: 200 }, (curr, prev) => {
      // Check if file was modified
      if (curr.mtime > prev.mtime) {
        try {
          const stateData = fs.readFileSync(sharedStatePath, 'utf8');
          const state = JSON.parse(stateData);

          console.log('[LAUNCHER] Shared state updated:', state);

          // Check if ML libraries are ready
          if (state.mlLibrariesReady) {
            console.log('[LAUNCHER] Received ML libraries ready signal from main app');

            // Stop watching the file
            fs.unwatchFile(sharedStatePath);

            // Update state to signal that we want to show settings
            state.showSettings = true;
            state.launcherClosing = true;
            state.timestamp = Date.now();

            try {
              fs.writeFileSync(sharedStatePath, JSON.stringify(state, null, 2));
              console.log('[LAUNCHER] Updated shared state to show settings');
            } catch (error) {
              console.error('[LAUNCHER] Failed to update shared state:', error);
            }

            // Close launcher and show settings
            this.closeLauncher();
          }
        } catch (error) {
          console.error('[LAUNCHER] Error reading shared state:', error);
        }
      }
    });

    // Set a timeout in case the main app doesn't respond
    setTimeout(() => {
      console.log('[LAUNCHER] Timeout waiting for ML libraries, showing settings anyway...');

      // Stop watching
      fs.unwatchFile(sharedStatePath);

      // Clean up shared state file
      try {
        if (fs.existsSync(sharedStatePath)) {
          fs.unlinkSync(sharedStatePath);
        }
      } catch (error) {
        // Ignore cleanup errors
      }

      // Close launcher anyway
      this.closeLauncher();
    }, 30000); // 30 second timeout

    // Set up timeout as fallback
    const maxWaitTime = 60000; // Maximum 60 seconds
    this.launchTimeout = setTimeout(() => {
      console.log('[LAUNCHER] Timeout waiting for main app readiness (60s), closing anyway');
      this.closeLauncher();
    }, maxWaitTime);
  }

  /**
   * Close the launcher properly and show settings window
   */
  private closeLauncher(): void {
    console.log('[LAUNCHER] Closing launcher and showing settings window');

    // Clear any pending timeout
    if (this.launchTimeout) {
      clearTimeout(this.launchTimeout);
      this.launchTimeout = null;
    }

    // Close the launcher window
    closeLauncherWindow();

    // Show the settings window
    setTimeout(() => {
      showSettingsWindow();
    }, 100);
  }

  /**
   * Exit launcher
   */
  async exitLauncher(): Promise<boolean> {
    try {
      console.log('[LAUNCHER] Exiting launcher...');
      
      // Cancel any ongoing operations
      if (this.abortController) {
        this.abortController.abort();
        this.abortController = null;
      }

      return true;
    } catch (error) {
      console.error('[LAUNCHER] Error exiting launcher:', error);
      return false;
    }
  }

  /**
   * Clean up resources
   */
  public cleanup(): void {
    // Clear launch timeout
    if (this.launchTimeout) {
      clearTimeout(this.launchTimeout);
      this.launchTimeout = null;
    }

    // Cancel any ongoing operations
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }

    // Reset launching flag
    this.isLaunching = false;
  }

  /**
   * Dispose resources
   */
  dispose(): void {
    this.cleanup();
  }
}
