import '@src/main.css'
import React, { useEffect, useState } from 'react';
import ReactDOM from 'react-dom/client';
import { HashRouter, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { ThemeProvider } from '@src/components/layout/theme-provider';
import { Titlebar } from '@src/components/layout/titlebar';

import App from '@src/features/app-2/App';
import { AppSidebar, PreloadedPages } from '@src/components/layout/app-sidebar';
import { SidebarProvider } from '@src/components/ui/sidebar';
import { ScrollArea } from '@radix-ui/react-scroll-area';
import { LauncherPage } from '@src/features/launcher/page';

const GlobalProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
      {children}
    </ThemeProvider>
  );
};

// Use named export for consistent components exports
export function AuthLayout() {
  return <App />;
}

export function MainLayout() {
  return (
    <SidebarProvider defaultOpen={window.innerWidth >= 800}>
      <AppSidebar />
      <div className="flex-1 overflow-hidden bg-sidebar">
        <div className="h-[calc(100%-2rem)] bg-background border-7 border-sidebar rounded-3xl overflow-hidden border-l-0 border-t-0">
          <ScrollArea className="h-full">
            <div className="p-6">
              <div className="container mx-auto ">
                <PreloadedPages />
              </div>
            </div>
          </ScrollArea>
        </div>
      </div>
    </SidebarProvider>
  );
}

export function LauncherLayout() {
  return <LauncherPage />;
}

// Component to check environment and redirect appropriately
function EnvironmentRouter() {
  const [initialRoute, setInitialRoute] = useState<string | null>(null);

  useEffect(() => {
    const checkEnvironment = async () => {
      try {
        if (!window.electron?.getLauncherEnv) {
          // No launcher env method available, go to auth
          setInitialRoute('/auth');
          return;
        }

        const env = await window.electron.getLauncherEnv();
        const isMicrosoft = env.IS_MICROSOFT === 'true';

        console.log('[MAIN] Environment check - isMicrosoft:', isMicrosoft);

        if (isMicrosoft) {
          // Microsoft Store build - start with launcher
          setInitialRoute('/launcher');
        } else {
          // Regular build - start with auth
          setInitialRoute('/auth');
        }
      } catch (error) {
        console.error('[MAIN] Failed to check environment:', error);
        // Default to auth on error
        setInitialRoute('/auth');
      }
    };

    checkEnvironment();
  }, []);

  // Show loading while determining route
  if (initialRoute === null) {
    return (
      <div className="min-h-screen bg-sidebar flex items-center justify-center">
        <div className="text-foreground">Loading...</div>
      </div>
    );
  }

  return <Navigate to={initialRoute} replace />;
}

// Component to conditionally render titlebar based on route
function ConditionalTitlebar({ children }: { children: React.ReactNode }) {
  const location = useLocation();

  // Routes that should NOT have a titlebar
  const routesWithoutTitlebar = ['/launcher'];
  const shouldHideTitlebar = routesWithoutTitlebar.includes(location.pathname);

  if (shouldHideTitlebar) {
    return <>{children}</>;
  }

  return <Titlebar>{children}</Titlebar>;
}

// Initialize the application
const rootElement = document.getElementById("root");
if (rootElement) {
  const root = ReactDOM.createRoot(rootElement);
  root.render(
    <React.StrictMode>
      <GlobalProviders>
        <HashRouter>
          <ConditionalTitlebar>
            <Routes>
              <Route path="/" element={<EnvironmentRouter />} />
              <Route path="/launcher" element={<LauncherLayout />} />
              <Route path="/auth" element={<AuthLayout />} />
              <Route element={<MainLayout />}>
                <Route path="/home" element={null} />
                <Route path="/modes" element={null} />
                <Route path="/text-replacements" element={null} />
                <Route path="/vocabulary" element={null} />
                <Route path="/ai-models" element={null} />
                <Route path="/settings" element={null} />
                <Route path="/history" element={null} />
              </Route>
              {/* Redirect based on environment */}
              <Route path="*" element={<EnvironmentRouter />} />
            </Routes>
          </ConditionalTitlebar>
        </HashRouter>
      </GlobalProviders>
    </React.StrictMode>
  );
}