import '@src/main.css'
import React, { useEffect, useState } from 'react';
import ReactDOM from 'react-dom/client';
import { HashRouter, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { ThemeProvider } from '@src/components/layout/theme-provider';
import { Titlebar } from '@src/components/layout/titlebar';

import App from '@src/features/app-2/App';
import { AppSidebar, PreloadedPages } from '@src/components/layout/app-sidebar';
import { SidebarProvider } from '@src/components/ui/sidebar';
import { ScrollArea } from '@radix-ui/react-scroll-area';
import { LauncherPage } from '@src/features/launcher/page';

const GlobalProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
      {children}
    </ThemeProvider>
  );
};

// Use named export for consistent components exports
export function AuthLayout() {
  return (
    <Titlebar>
      <App />
    </Titlebar>
  );
}

export function MainLayout() {
  return (
    <Titlebar>
      <SidebarProvider defaultOpen={window.innerWidth >= 800}>
        <AppSidebar />
        <div className="flex-1 overflow-hidden bg-sidebar">
          <div className="h-[calc(100%-2rem)] bg-background border-7 border-sidebar rounded-3xl overflow-hidden border-l-0 border-t-0">
            <ScrollArea className="h-full">
              <div className="p-6">
                <div className="container mx-auto ">
                  <PreloadedPages />
                </div>
              </div>
            </ScrollArea>
          </div>
        </div>
      </SidebarProvider>
    </Titlebar>
  );
}

// Launcher component (no titlebar)
export function LauncherComponent() {
  return <LauncherPage />;
}

// Main app component that decides what to render based on environment
function AppRoot() {
  const [appMode, setAppMode] = useState<'loading' | 'launcher' | 'main'>('loading');

  useEffect(() => {
    const checkEnvironment = async () => {
      try {
        // Check if we have a hash route (settings window)
        const currentHash = window.location.hash;
        if (currentHash && currentHash !== '#/') {
          console.log('[MAIN] Hash route detected:', currentHash, '- using main app');
          setAppMode('main');
          return;
        }

        if (!window.electron?.getLauncherEnv) {
          // No launcher env method available, go to main app
          setAppMode('main');
          return;
        }

        const env = await window.electron.getLauncherEnv();
        const isMicrosoft = env.IS_MICROSOFT === 'true';

        console.log('[MAIN] Environment check - isMicrosoft:', isMicrosoft, 'hash:', currentHash);

        if (isMicrosoft && !currentHash) {
          // Microsoft Store build with no hash - start with launcher
          setAppMode('launcher');
        } else {
          // Regular build or settings window - start with main app
          setAppMode('main');
        }
      } catch (error) {
        console.error('[MAIN] Failed to check environment:', error);
        // Default to main app on error
        setAppMode('main');
      }
    };

    checkEnvironment();
  }, []);

  // Listen for launcher completion to switch to main app
  useEffect(() => {
    if (appMode === 'launcher' && window.electron) {
      const handleLauncherComplete = () => {
        console.log('[MAIN] Launcher completed, switching to main app');
        setAppMode('main');
      };

      // Listen for backend completion (sent when launcher finishes extraction)
      const cleanup = window.electron.onBackendComplete(handleLauncherComplete);

      return cleanup;
    }
  }, [appMode]);

  // Show loading while determining mode
  if (appMode === 'loading') {
    return (
      <div className="min-h-screen bg-sidebar flex items-center justify-center">
        <div className="text-foreground">Loading...</div>
      </div>
    );
  }

  // Show launcher for Microsoft builds
  if (appMode === 'launcher') {
    return <LauncherComponent />;
  }

  // Show main app (with routing)
  return (
    <HashRouter>
      <Routes>
        <Route path="/" element={<Navigate to="/auth" replace />} />
        <Route path="/auth" element={<AuthLayout />} />
        <Route element={<MainLayout />}>
          <Route path="/home" element={null} />
          <Route path="/modes" element={null} />
          <Route path="/text-replacements" element={null} />
          <Route path="/vocabulary" element={null} />
          <Route path="/ai-models" element={null} />
          <Route path="/settings" element={null} />
          <Route path="/history" element={null} />
        </Route>
        <Route path="*" element={<Navigate to="/auth" replace />} />
      </Routes>
    </HashRouter>
  );
}

// Initialize the application
const rootElement = document.getElementById("root");
if (rootElement) {
  const root = ReactDOM.createRoot(rootElement);
  root.render(
    <React.StrictMode>
      <GlobalProviders>
        <AppRoot />
      </GlobalProviders>
    </React.StrictMode>
  );
}