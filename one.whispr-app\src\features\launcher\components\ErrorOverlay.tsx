import React from 'react';

interface ErrorOverlayProps {
  error: string;
}

/**
 * Simple error overlay that displays over the main content
 */
export const ErrorOverlay: React.FC<ErrorOverlayProps> = ({ error }) => {
  return (
    <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-sidebar border border-red-400/20 rounded-lg p-6 max-w-md mx-4">
        <div className="text-red-400 text-sm text-center">
          {error}
        </div>
      </div>
    </div>
  );
};
